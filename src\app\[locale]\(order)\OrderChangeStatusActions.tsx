"use client";

import { updateOrderStatus } from "@/app/[locale]/cms/actions";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useRouter } from "@/i18n/navigation";
import { OrderStatus } from "@generated/prisma";
import { useTranslations } from "next-intl";
import { useTransition } from "react";
import { toast } from "sonner";

type OrderCardAdminActionsProps = {
  isOpen: boolean;
  newStatus: OrderStatus | null;
  title: string;
  description: string;
  orderId: string;
  onClose: () => void;
};

export default function OrderChangeStatusActions({
  description,
  isOpen,
  newStatus,
  onClose,
  title,
  orderId,
}: OrderCardAdminActionsProps) {
  const tCMS = useTranslations("CMS");
  const [isPending, startTransition] = useTransition();
  const { refresh } = useRouter();

  const confirmStatusChange = () => {
    if (!newStatus) return;

    startTransition(async () => {
      try {
        const result = await updateOrderStatus({
          orderId,
          newStatus,
        });

        if (result.success) {
          toast.success(tCMS("actions.updateSuccess"));
          refresh();
        } else {
          toast.error(result.data);
        }
      } catch (error) {
        toast.error(tCMS("actions.updateError"));
      } finally {
        onClose();
      }
    });
  };

  return (
    <>
      <AlertDialog open={isOpen} onOpenChange={onClose}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{title}</AlertDialogTitle>
            <AlertDialogDescription>{description}</AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{tCMS("actions.cancel")}</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmStatusChange}
              disabled={isPending}
            >
              {isPending ? tCMS("actions.updating") : tCMS("actions.confirm")}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
