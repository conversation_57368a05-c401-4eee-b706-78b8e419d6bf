generator client {
  provider      = "prisma-client-js"
  output        = "../generated/prisma"
  binaryTargets = ["native", "rhel-openssl-3.0.x"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id            String    @id
  name          String
  email         String
  emailVerified Boolean
  image         String?
  createdAt     DateTime
  updatedAt     DateTime
  sessions      Session[]
  accounts      Account[]

  phoneNumber String?
  Order       Order[]

  role       String?
  banned     Boolean?
  banReason  String?
  banExpires DateTime?

  @@unique([email])
  @@map("user")
}

model Session {
  id        String   @id
  expiresAt DateTime
  token     String
  createdAt DateTime
  updatedAt DateTime
  ipAddress String?
  userAgent String?
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  impersonatedBy String?

  @@unique([token])
  @@map("session")
}

model Account {
  id                    String    @id
  accountId             String
  providerId            String
  userId                String
  user                  User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  accessToken           String?
  refreshToken          String?
  idToken               String?
  accessTokenExpiresAt  DateTime?
  refreshTokenExpiresAt DateTime?
  scope                 String?
  password              String?
  createdAt             DateTime
  updatedAt             DateTime

  @@map("account")
}

model Verification {
  id         String    @id
  identifier String
  value      String
  expiresAt  DateTime
  createdAt  DateTime?
  updatedAt  DateTime?

  @@map("verification")
}

model Cart {
  id        Int      @id @default(autoincrement())
  userId    String   @unique
  items     Json     @default("{}")
  updatedAt DateTime @updatedAt
  version   Int      @default(1)

  @@map("cart")
}

enum PaymentMethod {
  BANK_TRANSFER
  MULTICAIXA_EXPRESS
  PAY_IN_STORE
}

enum OrderStatus {
  PENDING
  COMPLETED
  CANCELLED
}

model Order {
  id            String        @id
  storeName     String
  storeAddress  String
  firstName     String
  lastName      String
  email         String
  phone         String
  total         Float
  items         Json
  paymentMethod PaymentMethod @default(BANK_TRANSFER)
  status        OrderStatus   @default(PENDING)
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt

  userId String?
  user   User?   @relation(fields: [userId], references: [id], onDelete: SetNull)

  @@map("order")
}
